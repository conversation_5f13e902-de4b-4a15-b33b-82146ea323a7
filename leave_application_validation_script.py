# Server Script for Leave Application Validation
# Event: Before Save
# This script validates if holidays immediately follow the leave end date
# when the Leave Type has include_holiday checked

# Skip validation if required fields are missing
if not (doc.employee and doc.leave_type and doc.to_date):
    pass
else:
    # Check if Leave Type has include_holiday checked
    include_holiday = frappe.db.get_value("Leave Type", doc.leave_type, "include_holiday")

    if include_holiday:
        # Get employee's holiday list
        holiday_list = frappe.db.get_value("Employee", doc.employee, "holiday_list")

        if not holiday_list:
            # Try to get company's default holiday list
            company = frappe.db.get_value("Employee", doc.employee, "company")
            if company:
                holiday_list = frappe.db.get_value("Company", company, "default_holiday_list")

        if holiday_list:
            # Convert to_date to date object
            leave_end_date = frappe.utils.getdate(doc.to_date)

            # Check for holidays immediately after leave end date
            # We'll check the next 7 days to find the next working day
            next_working_day = None

            # Find the next working day (non-holiday)
            for i in range(1, 8):  # Check next 7 days
                check_date = frappe.utils.add_days(leave_end_date, i)

                # Check if this date is a holiday
                is_holiday = frappe.db.exists("Holiday", {
                    "parent": holiday_list,
                    "holiday_date": check_date
                })

                if not is_holiday:
                    next_working_day = check_date
                    break

            # If we found a next working day, check if there are holidays between
            # leave end date and next working day
            if next_working_day:
                # Check if there are any holidays between to_date and next_working_day
                holidays_between = frappe.db.sql("""
                    SELECT holiday_date
                    FROM `tabHoliday`
                    WHERE parent = %s
                    AND holiday_date > %s
                    AND holiday_date < %s
                    ORDER BY holiday_date
                """, (holiday_list, leave_end_date, next_working_day))

                if holidays_between:
                    frappe.throw("Set Correct End Date")
